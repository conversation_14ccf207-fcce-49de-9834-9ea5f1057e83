{"name": "chefs-way-frontend", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "openapi": "node openapi.config.js"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@vue-flow/core": "^1.46.0", "ant-design-vue": "^4.2.6", "axios": "^1.7.2", "dayjs": "^1.11.13", "dexie": "^4.0.11", "lodash-es": "^4.17.21", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.1.2", "tslib": "^2.8.1", "vue": "^3.5.18", "vue-router": "^4.5.1", "vue-waterfall-plugin-next": "^2.6.9"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/lodash-es": "^4.17.12", "@types/node": "^22.16.5", "@umijs/openapi": "^1.13.15", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.31.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "less": "^4.4.1", "less-loader": "^12.3.0", "npm-run-all2": "^8.0.4", "prettier": "3.6.2", "typescript": "~5.8.0", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^3.0.4"}}