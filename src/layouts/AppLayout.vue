<template>
  <a-layout class="app-layout">
    <!-- 侧边栏 -->
    <GlobalSider />
    
    <!-- 主内容区 -->
    <a-layout class="app-content">
     <!-- 顶部导航栏 -->
      <GlobalHeader />
      <!-- 页面内容 -->
      <a-layout-content class="app-main">
        <router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, watch, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

import { useLoginUserStore } from '@/stores/userLoginStore'
import GlobalSider from '@/components/common/GlobalSider.vue'
import GlobalHeader from '@/components/common/GlobalHeader.vue'
/**
 * 应用主布局组件
 * 包含侧边栏导航、顶部工具栏和主内容区域
 */

const route = useRoute()
const router = useRouter()
const userStore = useLoginUserStore()

// 响应式状态
const selectedKeys = ref<string[]>([])



// 监听路由变化，更新选中的菜单项
watch(
  () => route.path,
  (newPath) => {
    if (newPath.includes('/app/dashboard')) {
      selectedKeys.value = ['dashboard']
    } else if (newPath.includes('/app/recipes')) {
      selectedKeys.value = ['recipes']
    } else if (newPath.includes('/app/collections')) {
      selectedKeys.value = ['collections']
    } else if (newPath.includes('/app/favorites')) {
      selectedKeys.value = ['favorites']
    } else if (newPath.includes('/app/grocery')) {
      selectedKeys.value = ['grocery']
    } else if (newPath.includes('/app/profile')) {
      selectedKeys.value = ['profile']
    }
  },
  { immediate: true }
)

// 事件处理
const handleLogout = async () => {
  try {
    // TODO: 调用登出API
    // await userStore.logout()
    message.success('退出登录成功')
    await router.push('/user/login')
  } catch (error) {
    message.error('退出登录失败')
  }
}
</script>

<style lang="less" scoped>
.app-layout {
  min-height: 100vh;
}

.app-sider {
  background: #fff;
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
  
  .logo {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .app-menu {
    border-right: none;
    padding: 16px 0;
    
    :deep(.ant-menu-item) {
      margin: 4px 12px;
      border-radius: 8px;
      
      i {
        margin-right: 8px;
        width: 16px;
        text-align: center;
      }
      
      &:hover,
      &.ant-menu-item-selected {
        background-color: #fff2e8;
        color: #ff7a00;
      }
    }
  }
}

.app-content {
  background: #f5f5f5;
}

.app-header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: 10;
  
  .header-left {
    .trigger {
      font-size: 18px;
      line-height: 64px;
      cursor: pointer;
      transition: color 0.3s;
      
      &:hover {
        color: #ff7a00;
      }
    }
  }
  
  .header-right {
    :deep(.ant-btn) {
      border: none;
      box-shadow: none;
      
      &:hover {
        color: #ff7a00;
        background-color: #fff2e8;
      }
    }
  }
}

.app-main {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  min-height: calc(100vh - 112px);
  overflow: auto;
}

// 响应式适配
@media (max-width: 768px) {
  .app-sider {
    position: fixed;
    z-index: 1000;
    height: 100vh;
  }
  
  .app-main {
    margin: 16px;
    padding: 16px;
  }
}
</style>
