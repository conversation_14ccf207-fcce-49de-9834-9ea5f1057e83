<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <div class="error-illustration">
        <div class="error-code">404</div>
        <div class="error-icon">
          <i class="fa fa-search"></i>
        </div>
      </div>
      
      <h1 class="error-title">页面不存在</h1>
      <p class="error-description">
        抱歉，您访问的页面不存在或已被移除。
      </p>
      
      <div class="error-actions">
        <a-space :size="16">
          <a-button 
            type="primary" 
            size="large"
            @click="$router.push('/')"
          >
            返回首页
          </a-button>
          
          <a-button 
            size="large"
            @click="$router.back()"
          >
            返回上页
          </a-button>
        </a-space>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 404错误页面
 */
</script>

<style lang="less" scoped>
.not-found-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 24px;
}

.not-found-content {
  text-align: center;
  max-width: 500px;
  
  .error-illustration {
    position: relative;
    margin-bottom: 40px;
    
    .error-code {
      font-size: 120px;
      font-weight: 700;
      color: #ff7a00;
      line-height: 1;
      margin-bottom: 20px;
    }
    
    .error-icon {
      font-size: 48px;
      color: #999;
    }
  }
  
  .error-title {
    font-size: 32px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 16px;
  }
  
  .error-description {
    font-size: 16px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 40px;
  }
  
  .error-actions {
    :deep(.ant-btn-primary) {
      background: #ff7a00;
      border-color: #ff7a00;
      
      &:hover {
        background: rgba(255, 122, 0, 0.9);
        border-color: rgba(255, 122, 0, 0.9);
      }
    }
  }
}

// 响应式适配
@media (max-width: 480px) {
  .not-found-content {
    .error-illustration .error-code {
      font-size: 80px;
    }
    
    .error-title {
      font-size: 24px;
    }
    
    .error-actions {
      :deep(.ant-space-item) {
        margin-bottom: 8px;
      }
    }
  }
}
</style>
