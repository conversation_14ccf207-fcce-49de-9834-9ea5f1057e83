<template>
  <div class="login-container">
    <div class="login-content">
      <!-- Logo和标题 -->
      <div class="login-header">
        <AppLogo 
          size="large"
          variant="colored"
          text="知食煮意"
          :showText="true"
        />
      </div>
      
      <!-- 登录表单 -->
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        @finish="handleSubmit"
        @finish-failed="handleSubmitFailed"
        class="login-form"
        layout="vertical"
      >
        <a-form-item 
          name="userAccount"
          label="邮箱"
        >
          <a-input
            v-model:value="formData.userAccount"
            size="large"
            placeholder="请输入邮箱地址"
            autocomplete="email"
          >
            <template #prefix>
              <MailOutlined class="input-icon" />
            </template>
          </a-input>
        </a-form-item>
        
        <a-form-item 
          name="userPassword"
          label="密码"
        >
          <a-input-password
            v-model:value="formData.userPassword"
            size="large"
            placeholder="请输入密码"
            autocomplete="current-password"
          >
            <template #prefix>
              <LockOutlined class="input-icon" />
            </template>
          </a-input-password>
        </a-form-item>
        
        <a-form-item>
          <div class="form-options">
            <a-checkbox v-model:checked="formData.remember">
              记住我
            </a-checkbox>
            
            <a class="forgot-password" @click="handleForgotPassword">
              忘记密码？
            </a>
          </div>
        </a-form-item>
        
        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            block
            :loading="loading"
            class="login-button"
          >
            登录
          </a-button>
        </a-form-item>
        
        <div class="register-link">
          <span>还没有账号？</span>
          <router-link to="/user/register" class="register-text">
            立即注册
          </router-link>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import type { FormInstance, Rule } from 'ant-design-vue/es/form'
import { MailOutlined, LockOutlined } from '@ant-design/icons-vue'
import { useLoginUserStore } from '@/stores/userLoginStore'
import { userLoginUsingPost } from '@/api/userController'
import { AppLogo } from '@/components'

/**
 * 登录页面组件
 * 对应HTML原型中的zh-login.html
 */

interface LoginForm {
  userAccount: string
  userPassword: string
  remember: boolean
}

const route = useRoute()
const router = useRouter()
const userStore = useLoginUserStore()

// 表单引用
const formRef = ref<FormInstance>()

// 响应式状态
const loading = ref<boolean>(false)
const formData = reactive<LoginForm>({
  userAccount: '',
  userPassword: '',
  remember: false
})

// 表单验证规则
const formRules: Record<string, Rule[]> = {
  // userAccount: [
  //   { required: true, message: '请输入邮箱地址', trigger: 'blur' },
  //   { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  // ],
  userPassword: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6个字符', trigger: 'blur' }
  ]
}

// 事件处理器
const handleSubmit = async (values: LoginForm) => {
  loading.value = true
  
  try {
    const res = await userLoginUsingPost({
      userAccount: values.userAccount,
      userPassword: values.userPassword
    })
    
    if (res.data.code === 0 && res.data.data) {
      // 保存用户信息到Store
      userStore.setLoginUser(res.data.data)
      
      message.success('登录成功！')
      
      // 跳转到目标页面或默认页面
      const redirect = route.query.redirect as string
      await router.push(redirect || '/app/dashboard')
    } else {
      message.error(res.data.message || '登录失败')
    }
  } catch (error: any) {
    console.error('登录失败:', error)
    const errorMessage = error.response?.data?.message || error.message || '登录失败，请检查网络连接'
    message.error(errorMessage)
  } finally {
    loading.value = false
  }
}

const handleSubmitFailed = (errorInfo: any) => {
  console.log('表单验证失败:', errorInfo)
  message.error('请检查表单填写')
}

const handleForgotPassword = () => {
  // TODO: 实现密码重置功能
  message.info('密码重置功能开发中...')
}
</script>

<style lang="less" scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
}

.login-content {
  background: #fff;
  border-radius: 24px;
  padding: 48px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-form {
  :deep(.ant-form-item-label > label) {
    font-weight: 500;
    color: #1a1a1a;
  }
  
  .input-icon {
    color: #999;
  }
  
  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .forgot-password {
      color: #ff7a00;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
  
  .login-button {
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 12px;
    background: #ff7a00;
    border-color: #ff7a00;
    margin-top: 16px;
    
    &:hover {
      background: rgba(255, 122, 0, 0.9);
      border-color: rgba(255, 122, 0, 0.9);
    }
  }
}

.register-link {
  text-align: center;
  margin-top: 24px;
  color: #666;
  
  .register-text {
    color: #ff7a00;
    font-weight: 500;
    margin-left: 8px;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

// 响应式适配
@media (max-width: 480px) {
  .login-content {
    padding: 32px 24px;
    border-radius: 16px;
  }
  

}
</style>
