<template>
  <div class="components-demo">
    <div class="demo-container">
      <h1 class="demo-title">组件展示页面</h1>
      <p class="demo-description">展示所有已创建的可复用组件及其不同变体</p>
      
      <!-- AppLogo 组件展示 -->
      <section class="demo-section">
        <h2 class="section-title">AppLogo 组件</h2>
        <div class="demo-grid">
          <div class="demo-item">
            <h4>默认样式 - 中等尺寸</h4>
            <AppLogo />
          </div>
          
          <div class="demo-item">
            <h4>彩色样式 - 大尺寸</h4>
            <AppLogo size="large" variant="colored" />
          </div>
          
          <div class="demo-item">
            <h4>白色样式 - 小尺寸</h4>
            <div class="dark-bg">
              <AppLogo size="small" variant="white" />
            </div>
          </div>
          
          <div class="demo-item">
            <h4>简约样式 - 可点击</h4>
            <AppLogo variant="minimal" :clickable="true" @click="handleLogoClick" />
          </div>
        </div>
      </section>
      
      <!-- UserAvatar 组件展示 -->
      <section class="demo-section">
        <h2 class="section-title">UserAvatar 组件</h2>
        <div class="demo-grid">
          <div class="demo-item">
            <h4>默认头像</h4>
            <UserAvatar userName="张三" />
          </div>
          
          <div class="demo-item">
            <h4>带图片 + 在线状态</h4>
            <UserAvatar 
              src="https://picsum.photos/seed/user1/100/100"
              userName="李四"
              :showOnlineStatus="true"
              :isOnline="true"
            />
          </div>
          
          <div class="demo-item">
            <h4>认证用户 + 用户信息</h4>
            <UserAvatar 
              userName="王五"
              userRole="管理员"
              :verified="true"
              :showInfo="true"
              size="large"
            />
          </div>
          
          <div class="demo-item">
            <h4>方形 + 边框</h4>
            <UserAvatar 
              userName="赵六"
              shape="square"
              :bordered="true"
              size="extra-large"
            />
          </div>
        </div>
      </section>
      
      <!-- SearchInput 组件展示 -->
      <section class="demo-section">
        <h2 class="section-title">SearchInput 组件</h2>
        <div class="demo-grid">
          <div class="demo-item">
            <h4>基础搜索框</h4>
            <SearchInput 
              v-model="searchValue1"
              placeholder="搜索食谱..."
            />
          </div>
          
          <div class="demo-item">
            <h4>带搜索按钮</h4>
            <SearchInput 
              v-model="searchValue2"
              placeholder="搜索用户..."
              :showSearchButton="true"
              @search="handleSearch"
            />
          </div>
          
          <div class="demo-item">
            <h4>带建议列表</h4>
            <SearchInput 
              v-model="searchValue3"
              placeholder="搜索建议..."
              :suggestions="searchSuggestions"
              :showSuggestions="true"
              @select-suggestion="handleSelectSuggestion"
            />
          </div>
          
          <div class="demo-item">
            <h4>加载状态</h4>
            <SearchInput 
              v-model="searchValue4"
              placeholder="搜索中..."
              :loading="true"
              :showResultCount="true"
              :resultCount="42"
            />
          </div>
        </div>
      </section>
      
      <!-- RecipeCard 组件展示 -->
      <section class="demo-section">
        <h2 class="section-title">RecipeCard 组件</h2>
        <div class="demo-grid">
          <div class="demo-item">
            <h4>默认卡片</h4>
            <RecipeCard 
              :recipe="sampleRecipe"
              @click="handleRecipeClick"
            />
          </div>
          
          <div class="demo-item">
            <h4>特色推荐</h4>
            <RecipeCard 
              :recipe="featuredRecipe"
              :featured="true"
              :isFavorited="true"
              @toggle-favorite="handleToggleFavorite"
            />
          </div>
          
          <div class="demo-item">
            <h4>小尺寸卡片</h4>
            <RecipeCard 
              :recipe="smallRecipe"
              size="small"
              :showFooterActions="true"
              @share="handleShare"
            />
          </div>
          
          <div class="demo-item">
            <h4>水平布局</h4>
            <RecipeCard 
              :recipe="horizontalRecipe"
              layout="horizontal"
              size="large"
              :showEditAction="true"
              @edit="handleEdit"
            />
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { AppLogo, UserAvatar, SearchInput, RecipeCard } from '@/components'

/**
 * 组件展示页面
 * 用于展示所有可复用组件的不同变体和使用方式
 */

// 搜索相关状态
const searchValue1 = ref('')
const searchValue2 = ref('')
const searchValue3 = ref('')
const searchValue4 = ref('')

// 搜索建议数据
const searchSuggestions = ref([
  {
    text: '红烧肉',
    description: '经典川菜',
    type: '菜谱',
    icon: 'fa fa-cutlery'
  },
  {
    text: '蛋炒饭',
    description: '快手家常菜',
    type: '菜谱',
    icon: 'fa fa-cutlery'
  },
  {
    text: '张三',
    description: '美食达人',
    type: '用户',
    icon: 'fa fa-user'
  }
])

// 示例食谱数据
const sampleRecipe = ref({
  id: 1,
  name: '宫保鸡丁',
  introduction: '经典川菜，口感鲜美，制作简单，老少皆宜的家常菜品。',
  coverKey: 'https://picsum.photos/seed/recipe1/400/300',
  tags: ['川菜', '家常', '下饭'],
  category: '热菜',
  stepCount: 8,
  cookTime: 25,
  servings: 3,
  difficulty: 'medium' as const,
  rating: 4.6,
  viewCount: 1250,
  favoriteCount: 89,
  estimatedCost: 18
})

const featuredRecipe = ref({
  id: 2,
  name: '红烧狮子头',
  introduction: '传统江苏名菜，肉质鲜嫩，汤汁浓郁，营养丰富。',
  coverKey: 'https://picsum.photos/seed/recipe2/400/300',
  tags: ['苏菜', '精品', '营养'],
  category: '热菜',
  stepCount: 12,
  cookTime: 45,
  servings: 4,
  difficulty: 'hard' as const,
  rating: 4.8,
  viewCount: 2100,
  favoriteCount: 156,
  estimatedCost: 35
})

const smallRecipe = ref({
  id: 3,
  name: '西红柿鸡蛋',
  introduction: '简单易做的家常菜',
  coverKey: 'https://picsum.photos/seed/recipe3/400/300',
  tags: ['家常', '快手'],
  cookTime: 10,
  difficulty: 'easy' as const,
  rating: 4.2
})

const horizontalRecipe = ref({
  id: 4,
  name: '麻婆豆腐',
  introduction: '经典川菜代表作，麻辣鲜香，豆腐嫩滑，是下饭神器。制作工艺考究，调料丰富。',
  coverKey: 'https://picsum.photos/seed/recipe4/400/300',
  tags: ['川菜', '经典', '麻辣', '下饭'],
  category: '热菜',
  stepCount: 6,
  cookTime: 20,
  servings: 2,
  difficulty: 'medium' as const,
  rating: 4.7,
  viewCount: 1800,
  favoriteCount: 134,
  estimatedCost: 12
})

// 事件处理器
const handleLogoClick = () => {
  message.info('Logo被点击了！')
}

const handleSearch = (value: string) => {
  message.success(`搜索: ${value}`)
}

const handleSelectSuggestion = (suggestion: any) => {
  message.info(`选择了建议: ${suggestion.text}`)
}

const handleRecipeClick = (recipe: any) => {
  message.info(`点击了食谱: ${recipe.name}`)
}

const handleToggleFavorite = (recipeId: number, isFavorited: boolean) => {
  message.success(isFavorited ? '已取消收藏' : '已添加到收藏')
}

const handleShare = (recipe: any) => {
  message.success(`分享食谱: ${recipe.name}`)
}

const handleEdit = (recipe: any) => {
  message.info(`编辑食谱: ${recipe.name}`)
}
</script>

<style lang="less" scoped>
.components-demo {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  background: #fff;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.demo-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 8px;
  text-align: center;
}

.demo-description {
  font-size: 16px;
  color: #666;
  text-align: center;
  margin-bottom: 48px;
}

.demo-section {
  margin-bottom: 48px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid #ff7a00;
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.demo-item {
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
  
  h4 {
    font-size: 14px;
    font-weight: 600;
    color: #666;
    margin: 0 0 16px;
    text-align: center;
  }
  
  .dark-bg {
    background: #1a1a1a;
    padding: 16px;
    border-radius: 8px;
    display: flex;
    justify-content: center;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .demo-container {
    padding: 20px;
  }
  
  .demo-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .demo-item {
    padding: 16px;
  }
}
</style>
