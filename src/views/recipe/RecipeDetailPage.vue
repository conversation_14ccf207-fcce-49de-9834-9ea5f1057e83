<template>
  <div class="recipe-detail">
    <a-row :gutter="[24, 0]">
      <!-- 左侧图片区域 -->
      <a-col :span="12" class="image-column">
        <div class="recipe-image-container">
          <a-image
            :src="recipe?.coverKey ? defaultCoverUrl : defaultCoverUrl"
            class="recipe-image"
          />
        </div>
      </a-col>

      <!-- 右侧内容区域 -->
      <a-col :span="12" class="recipe-content">
        <div class="recipe-header">
          <div class="recipe-meta">
            <span class="recipe-category">{{ recipe?.category }}</span>
            <span class="recipe-tag">{{ recipe?.tags }}</span>
          </div>
          <h1 class="recipe-title">{{ recipe?.name }}</h1>

          <!-- <div class="recipe-rating">
            <a-rate :default-value="5" disabled />
            <span class="review-count">120 Reviews</span>
          </div> -->
        </div>

        <p class="recipe-description">
          {{ recipe?.introduction }}
        </p>

        <div class="recipe-buttons">
          <a-button class="btn-ingredients" @click="openIngredientModal">查看食材</a-button>
          <a-button class="btn-cooking-steps">查看详细步骤</a-button>
        </div>
        <IngredientDetail
          :visible="showIngredientModal"
          :ingredients="customIngredients"
          @close="handleIngredientModalClose"
        />
        <!-- <div class="recipe-details">
          <div class="serving-info">
            <p class="serves-text">Serves 4</p>
            <div class="servings-control">
              <a-button class="quantity-btn" size="small" @click="decreaseQuantity">-</a-button>
              <span class="quantity">{{ quantity }}</span>
              <a-button class="quantity-btn" size="small" @click="increaseQuantity">+</a-button>
            </div>
          </div>
          <div class="price-container">
            <span class="price">$15</span>
          </div>
        </div>

        <a-button type="primary" class="btn-add-to-plan">Add to meal plan</a-button> -->
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { Row, Col, Button, Rate, message } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { get } from 'lodash-es'
import { getRecipeUsingGet } from '@/api/recipeController'
import defaultCoverUrlPath from '@/assest/images/DefaultImage.jpg'

const router = useRouter()
const route = useRoute()

const recipe = ref<API.RecipeVO>()
const defaultCoverUrl = ref<string>(defaultCoverUrlPath)
const showIngredientModal = ref(false)
const customIngredients = ref([
  { name: '高筋面粉', amount: '250g' },
  { name: '酵母', amount: '5g' },
  { name: '盐', amount: '3g' },
  { name: '温水', amount: '150ml' },
  { name: '橄榄油', amount: '10ml' },
])

// 打开弹窗
const openIngredientModal = () => {
  console.log('打开')
  showIngredientModal.value = true
}

// 关闭弹窗
const handleIngredientModalClose = () => {
  showIngredientModal.value = false
}
const fecthRecipe = async () => {
  const recipeId = route.params?.id
  console.log(recipeId)
  const res = await getRecipeUsingGet({ id: recipeId })
  if (res.data.code == 0 && res.data.data) {
    recipe.value = res.data.data
  } else {
    message.error(res.data.message)
  }
}

onMounted(() => {
  fecthRecipe()
})
</script>

<style scoped>
.recipe-detail {
  padding: 32px;
  max-width: 1200px;
  margin: 24px auto;
}

.image-column {
  display: flex;
  align-items: center;
  justify-content: center;
}

.recipe-image-container {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transition: transform 0.3s ease;
}

.recipe-image-container:hover {
  transform: translateY(-5px);
}

.recipe-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s ease;
}

.recipe-image-container:hover .recipe-image {
  transform: scale(1.03);
}

.recipe-content {
  padding-left: 32px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.recipe-header {
  margin-bottom: 24px;
}

.recipe-meta {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}

.recipe-category {
  font-size: 14px;
  color: #ff7a00;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
  padding: 4px 12px;
  background-color: rgba(255, 122, 0, 0.1);
  border-radius: 20px;
}

.recipe-tag {
  font-size: 14px;
  color: #666;
  padding: 4px 12px;
  background-color: #f5f5f5;
  border-radius: 20px;
}

.recipe-title {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 16px;
  color: #222;
  line-height: 1.2;
}

.recipe-rating {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.ant-rate {
  margin-right: 12px;
  color: #ffb400;
}

.review-count {
  color: #888;
  font-size: 14px;
}

.recipe-description {
  color: #555;
  line-height: 1.8;
  margin-bottom: 36px;
  font-size: 16px;
  padding-right: 16px;
}

.recipe-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 36px;
}

.btn-ingredients,
.btn-cooking-steps {
  background-color: #ff7a00;
  border-color: #ff7a00;
  color: white;
  /* padding: 10px 28px; */
  font-size: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  /* font-weight: 500; */
}

.btn-ingredients:hover,
.btn-cooking-steps:hover {
  background-color: #e06a00;
  border-color: #e06a00;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 122, 0, 0.2);
}

.recipe-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 32px;
}

.serving-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.serves-text {
  color: #666;
  margin: 0;
  font-size: 15px;
}

.servings-control {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-size: 18px;
}

.quantity-btn:hover {
  background-color: #f5f5f5;
  border-color: #ccc;
}

.quantity {
  padding: 0 20px;
  font-size: 18px;
  font-weight: 500;
}

.price-container {
  position: relative;
}

.price {
  font-size: 32px;
  font-weight: 700;
  color: #222;
  position: relative;
}

.price::before {
  content: '';
  position: absolute;
  bottom: 6px;
  left: 0;
  width: 100%;
  height: 8px;
  background-color: rgba(255, 122, 0, 0.15);
  z-index: -1;
}

.btn-add-to-plan {
  background-color: #ff7a00;
  border-color: #ff7a00;
  color: white;
  width: 100%;
  padding: 14px 0;
  font-size: 18px;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(255, 122, 0, 0.15);
}

.btn-add-to-plan:hover {
  background-color: #e06a00;
  border-color: #e06a00;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 122, 0, 0.25);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .recipe-container {
    padding: 16px;
    margin: 16px;
  }

  .recipe-title {
    font-size: 32px;
  }

  .recipe-content {
    padding-left: 0;
    margin-top: 24px;
  }

  .recipe-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .btn-ingredients,
  .btn-cooking-steps {
    width: 100%;
  }

  .recipe-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}
</style>
