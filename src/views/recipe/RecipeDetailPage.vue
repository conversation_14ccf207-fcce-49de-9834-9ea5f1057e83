<template>
  <div class="recipe-detail">
    <a-row :gutter="[24, 0]">
      <!-- 左侧图片区域 -->
      <a-col :span="12" class="image-column">
        <div class="recipe-image-container">
          <a-image
            :src="recipe?.coverKey ? defaultCoverUrl : defaultCoverUrl"
            class="recipe-image"
          />
        </div>
      </a-col>

      <!-- 右侧内容区域 -->
      <a-col :span="12" class="recipe-content">
        <div class="recipe-content-scrollable">
          <div class="recipe-header">
            <div class="recipe-meta">
              <span class="recipe-category">{{ recipe?.category }}</span>
              <span class="recipe-tag">{{ recipe?.tags }}</span>
            </div>
            <h1 class="recipe-title">{{ recipe?.name }}</h1>

            <!-- <div class="recipe-rating">
              <a-rate :default-value="5" disabled />
              <span class="review-count">120 Reviews</span>
            </div> -->
          </div>

          <p class="recipe-description">
            {{ recipe?.introduction }}
          </p>

          <!-- 原材料表格 -->
          <div class="ingredients-section">
            <h3 class="section-title">原材料清单</h3>
            <div class="ingredients-table-container">
              <a-table
                :columns="ingredientColumns"
                :data-source="customIngredients"
                :pagination="false"
                size="small"
                :scroll="{ y: 200 }"
                class="ingredients-table"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'name'">
                    <span class="ingredient-name">{{ record.name }}</span>
                  </template>
                  <template v-if="column.key === 'amount'">
                    <span class="ingredient-amount">{{ record.amount }}</span>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { Row, Col, Button, Rate, message, Table } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { get } from 'lodash-es'
import { getRecipeUsingGet } from '@/api/recipeController'
import defaultCoverUrlPath from '@/assest/images/DefaultImage.jpg'

const router = useRouter()
const route = useRoute()

const recipe = ref<any>()
const defaultCoverUrl = ref<string>(defaultCoverUrlPath)
const customIngredients = ref([
  { key: '1', name: '高筋面粉', amount: '250g' },
  { key: '2', name: '酵母', amount: '5g' },
  { key: '3', name: '盐', amount: '3g' },
  { key: '4', name: '温水', amount: '150ml' },
  { key: '5', name: '橄榄油', amount: '10ml' },
  { key: '6', name: '高筋面粉', amount: '250g' },
  { key: '7', name: '酵母', amount: '5g' },
  { key: '8', name: '盐', amount: '3g' },
  { key: '9', name: '温水', amount: '150ml' },
  { key: '10', name: '橄榄油', amount: '10ml' },
])

// 表格列配置
const ingredientColumns = [
  {
    title: '原材料',
    dataIndex: 'name',
    key: 'name',
    width: '60%',
  },
  {
    title: '用量',
    dataIndex: 'amount',
    key: 'amount',
    width: '40%',
  },
]
const fecthRecipe = async () => {
  const recipeId = route.params?.id
  console.log(recipeId)
  const res = await getRecipeUsingGet({ id: recipeId })
  if (res.data.code == 0 && res.data.data) {
    recipe.value = res.data.data
  } else {
    message.error(res.data.message)
  }
}

onMounted(() => {
  fecthRecipe()
})
</script>

<style scoped>
.recipe-detail {
  padding: 32px;
  max-width: 1200px;
  margin: 24px auto;
}

.image-column {
  display: flex;
  align-items: center;
  justify-content: center;
}

.recipe-image-container {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transition: transform 0.3s ease;
}

.recipe-image-container:hover {
  transform: translateY(-5px);
}

.recipe-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s ease;
}

.recipe-image-container:hover .recipe-image {
  transform: scale(1.03);
}

.recipe-content {
  padding-left: 32px;
  height: 600px; /* 固定高度 */
  overflow: hidden;
}

.recipe-content-scrollable {
  height: 100%;
  overflow-y: auto;
  padding-right: 8px;
  display: flex;
  flex-direction: column;
}

.recipe-content-scrollable::-webkit-scrollbar {
  width: 6px;
}

.recipe-content-scrollable::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.recipe-content-scrollable::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.recipe-content-scrollable::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.recipe-header {
  margin-bottom: 24px;
}

.recipe-meta {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}

.recipe-category {
  font-size: 14px;
  color: #ff7a00;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
  padding: 4px 12px;
  background-color: rgba(255, 122, 0, 0.1);
  border-radius: 20px;
}

.recipe-tag {
  font-size: 14px;
  color: #666;
  padding: 4px 12px;
  background-color: #f5f5f5;
  border-radius: 20px;
}

.recipe-title {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 16px;
  color: #222;
  line-height: 1.2;
}

.recipe-rating {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.ant-rate {
  margin-right: 12px;
  color: #ffb400;
}

.review-count {
  color: #888;
  font-size: 14px;
}

.recipe-description {
  color: #555;
  line-height: 1.8;
  margin-bottom: 24px;
  font-size: 16px;
  padding-right: 16px;
}

/* 原材料表格样式 */
.ingredients-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  border-bottom: 2px solid #ff7a00;
  padding-bottom: 8px;
}

.ingredients-table-container {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 隐藏表格内部滚动条 */
.ingredients-table :deep(.ant-table-body) {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.ingredients-table :deep(.ant-table-body)::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

.ingredients-table {
  margin: 0;
}

.ingredients-table :deep(.ant-table-thead > tr > th) {
  background-color: #ff7a00;
  color: white;
  font-weight: 600;
  border-bottom: none;
  text-align: center;
}

.ingredients-table :deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
}

.ingredients-table :deep(.ant-table-tbody > tr:hover > td) {
  background-color: #fff9f0;
}

.ingredient-name {
  font-weight: 500;
  color: #333;
}

.ingredient-amount {
  color: #ff7a00;
  font-weight: 600;
  text-align: right;
  display: block;
}

.recipe-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 32px;
}

.serving-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.serves-text {
  color: #666;
  margin: 0;
  font-size: 15px;
}

.servings-control {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-size: 18px;
}

.quantity-btn:hover {
  background-color: #f5f5f5;
  border-color: #ccc;
}

.quantity {
  padding: 0 20px;
  font-size: 18px;
  font-weight: 500;
}

.price-container {
  position: relative;
}

.price {
  font-size: 32px;
  font-weight: 700;
  color: #222;
  position: relative;
}

.price::before {
  content: '';
  position: absolute;
  bottom: 6px;
  left: 0;
  width: 100%;
  height: 8px;
  background-color: rgba(255, 122, 0, 0.15);
  z-index: -1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .recipe-detail {
    padding: 16px;
    margin: 16px;
  }

  .recipe-title {
    font-size: 32px;
  }

  .recipe-content {
    padding-left: 0;
    margin-top: 24px;
    height: 500px; /* 移动端稍微降低高度 */
  }

  .section-title {
    font-size: 18px;
  }

  .ingredients-table :deep(.ant-table-thead > tr > th),
  .ingredients-table :deep(.ant-table-tbody > tr > td) {
    padding: 8px 12px;
    font-size: 14px;
  }

  .recipe-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}
</style>
