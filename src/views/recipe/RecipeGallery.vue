<template>
  <div id="recipe-gallery">
    <h1 v-if="isLoading">Loading Recipes...</h1>
    <Waterfall
      v-else
      :list="recipeList"
      :breakpoints="breakpoints"
      :gutter="20"
      row-key="id"
      img-selector="coverImageUrl"
      background-color="transparent"
      :animation-duration="500"
      animation-effect="fadeIn"
    >
      <template #default="{ item }">
        <RecipeCard :recipe="item" hoverable show-actions :max-tags-display="3" />
      </template>
    </Waterfall>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import RecipeCard from '@/components/recipe/RecipeCard.vue' // Adjust the path to your RecipeCard component
import { useLoginUserStore } from '@/stores/userLoginStore'
import { listRecipesUsingPost } from '@/api/recipeController'
import { message } from 'ant-design-vue'

// This is a placeholder for your actual API type.
// You would typically import this from a generated API client.

// 1. Reactive state for the list of recipes
const recipeList = ref<API.RecipeVO[]>([])
const isLoading = ref(true)

// 2. Configuration for the Waterfall component's responsive behavior
const breakpoints = {
  1200: { rowPerView: 4 }, // 4 columns on screens > 1200px
  800: { rowPerView: 3 }, // 3 columns on screens > 800px
  500: { rowPerView: 2 }, // 2 columns on screens > 500px
  300: { rowPerView: 1 }, // 1 column on screens > 300px
}
const userLogin = useLoginUserStore()
const searchParams = reactive<API.RecipeQueryRequest>({
  id: userLogin.id,
  current: 1,
  pageSize: 10,
  sortOrder: 'descend',
  sortField: 'createTime',
})
const fetchRecipeList = async () => {
  const res = await listRecipesUsingPost(searchParams)
  console.log(res.data.data)
  if (res.data.code == 0 && res.data.data) {
    recipeList.value = res.data.data.records
    isLoading.value = false
  } else {
    message.error(res.data.message)
  }
}
// 3. Simulate fetching data from an API when the component is mounted
onMounted(() => {
  fetchRecipeList()
})
</script>

<style scoped>
#recipe-gallery {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}
</style>
