<template>
  <div id="ingrident-detail">
    <a-modal
      :visible="visible"
      :title="null"
      :footer="null"
      :closable="false"
      @cancel="handleClose"
      :body-style="{ padding: '0', borderRadius: '4px' }"
      :mask-closable="false"
      :width="400"
    >
      <!-- 弹窗内容容器 -->
      <div class="ingredient-modal">
        <!-- 标题栏 -->
        <div class="modal-header">
          <h2 class="modal-title">Ingredient List</h2>
          <a-button class="close-btn" @click="handleClose"> Close </a-button>
        </div>

        <!-- 配料列表 -->
        <div class="ingredient-list">
          <div v-for="(ingredient, index) in ingredients" :key="index" class="ingredient-item">
            <span class="ingredient-name">{{ ingredient.name }}</span>
            <span class="ingredient-amount">{{ ingredient.amount }}</span>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

// 定义组件接收的props
const props = defineProps({
  // 控制弹窗显示/隐藏
  visible: {
    type: Boolean,
    default: false,
  },
  // 配料数据，从外部传入
  ingredients: {
    type: Array,
    default: () => [
      { name: 'Flour', amount: '2 cups' },
      { name: 'Sugar', amount: '1 cup' },
      { name: 'Butter', amount: '100g' },
      { name: 'Eggs', amount: '3 pcs' },
      { name: 'Milk', amount: '500 ml' },
    ],
  },
})

// 定义事件发射
const emit = defineEmits(['close'])

// 处理关闭弹窗
const handleClose = () => {
  emit('close')
}
</script>

<style scoped>
.ingredient-modal {
  width: 100%;
  font-family: Arial, sans-serif;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background-color: #ff8c00;
  border: none;
  color: white;
  padding: 6px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.close-btn:hover {
  background-color: #e67e00;
  color: white;
}

.ingredient-list {
  padding: 0 20px;
}

.ingredient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 0;
  border-bottom: 1px solid #e0e0e0;
  font-size: 16px;
  color: #333;
}

/* 最后一项去掉底部边框 */
.ingredient-item:last-child {
  border-bottom: none;
}
</style>
