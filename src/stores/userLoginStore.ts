import { defineStore } from "pinia";
import { ref } from "vue";
import { message } from 'ant-design-vue'
import { getLoginUserUsingGet, userLogoutUsingPost } from "@/api/userController";

/**
 * 用户登录状态Store
 * 管理用户登录信息和认证状态
 */
export const useLoginUserStore = defineStore("loginUser", () => {
  const loginUser = ref<API.LoginUserVO | null>(null);

  /**
   * 获取当前登录用户信息
   */
  async function fetchLoginUser() {
    try {
      const res = await getLoginUserUsingGet();
      if (res.data.code === 0 && res.data.data) {
        loginUser.value = res.data.data;
        return res.data.data;
      } else {
        // 未登录或获取失败
        loginUser.value = null;
        return null;
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      loginUser.value = null;
      return null;
    }
  }

  /**
   * 设置登录用户信息
   */
  function setLoginUser(newLoginUser: API.LoginUserVO | null) {
    loginUser.value = newLoginUser;
  }

  /**
   * 用户登出
   */
  async function logout() {
    try {
      const res = await userLogoutUsingPost();
      if (res.data.code === 0) {
        loginUser.value = null;
        message.success('退出登录成功');
        return true;
      } else {
        message.error(res.data.message || '退出登录失败');
        return false;
      }
    } catch (error) {
      console.error('退出登录失败:', error);
      message.error('退出登录失败');
      return false;
    }
  }

  /**
   * 清除用户信息（用于强制登出）
   */
  function clearUser() {
    loginUser.value = null;
  }

  return { 
    loginUser, 
    setLoginUser, 
    fetchLoginUser, 
    logout,
    clearUser
  };
}
// TODO: 安装pinia-plugin-persistedstate后启用持久化
// , {
//   persist: {
//     key: 'chef-way-user',
//     storage: localStorage,
//     paths: ['loginUser']
//   }
// }
);

