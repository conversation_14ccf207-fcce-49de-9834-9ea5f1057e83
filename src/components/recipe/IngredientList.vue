<template>
  <div id="ingredient-list">
    <!-- 原材料表格 -->
    <div class="ingredients-section">
      <div class="ingredients-table-container">
        <a-table
          :columns="ingredientColumns"
          :data-source="ingredientList"
          :pagination="false"
          size="small"
          :scroll="{ y: 200 }"
          class="ingredients-table"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <span class="ingredient-name">{{ record.name }}</span>
            </template>
            <template v-if="column.key === 'amount'">
              <span class="ingredient-amount">{{ record.amount }}</span>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

interface Props {
  ingredientList: any[]
}

const props = withDefaults(defineProps<Props>(), {
  ingredientList: () => [],
})

// 表格列配置
const ingredientColumns = [
  {
    title: '原材料',
    dataIndex: 'name',
    key: 'name',
    width: '60%',
  },
  {
    title: '用量',
    dataIndex: 'amount',
    key: 'amount',
    width: '40%',
  },
]
</script>

<style scoped>
/* 原材料表格样式 */
.ingredients-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #4a4a4a;
  margin-bottom: 16px;
  border-bottom: 1px solid #e6e6e6;
  padding-bottom: 8px;
}

.ingredients-table-container {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

/* 隐藏表格内部滚动条 */
.ingredients-table :deep(.ant-table-body) {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.ingredients-table :deep(.ant-table-body)::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

.ingredients-table {
  margin: 0;
}

.ingredients-table :deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
  color: #6b6b6b;
  font-weight: 500;
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
}

.ingredients-table :deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid #f5f5f5;
  padding: 12px 16px;
  background-color: #ffffff;
}

.ingredients-table :deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f8f9fa;
}

.ingredient-name {
  font-weight: 500;
  color: #4a4a4a;
}

.ingredient-amount {
  color: #7a7a7a;
  font-weight: 500;
  text-align: right;
  display: block;
}
</style>
