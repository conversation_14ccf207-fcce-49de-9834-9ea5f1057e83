<template>
  <div 
    :class="[
      'app-logo',
      `app-logo--${size}`,
      `app-logo--${variant}`,
      { 'app-logo--clickable': clickable }
    ]"
    @click="handleClick"
  >
    <!-- Logo图标 -->
    <div class="logo-icon">
      <i class="fa fa-cutlery"></i>
    </div>
    
    <!-- Logo文字 -->
    <span v-if="showText" class="logo-text">
      {{ text }}
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

/**
 * 应用Logo组件
 * 
 * @description 可复用的应用Logo，支持多种尺寸和样式变体
 * <AUTHOR> Way Team
 */

interface Props {
  /** Logo尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** Logo变体样式 */
  variant?: 'default' | 'white' | 'colored' | 'minimal'
  /** 是否显示文字 */
  showText?: boolean
  /** 自定义文字内容 */
  text?: string
  /** 是否可点击 */
  clickable?: boolean
}

interface Emits {
  /** Logo点击事件 */
  (e: 'click'): void
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  variant: 'default',
  showText: true,
  text: 'Recipe Hub',
  clickable: false
})

const emit = defineEmits<Emits>()

// 处理点击事件
const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}
</script>

<style lang="less" scoped>
.app-logo {
  display: inline-flex;
  align-items: center;
  user-select: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &--clickable {
    cursor: pointer;
    
    &:hover {
      transform: translateY(-1px);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  .logo-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    flex-shrink: 0;
    
    i {
      transition: all 0.3s ease;
    }
  }
  
  .logo-text {
    font-weight: 700;
    margin-left: 12px;
    white-space: nowrap;
    transition: all 0.3s ease;
  }
  
  // 尺寸变体
  &--small {
    .logo-icon {
      width: 28px;
      height: 28px;
      
      i {
        font-size: 14px;
      }
    }
    
    .logo-text {
      font-size: 14px;
      margin-left: 8px;
    }
  }
  
  &--medium {
    .logo-icon {
      width: 40px;
      height: 40px;
      
      i {
        font-size: 20px;
      }
    }
    
    .logo-text {
      font-size: 18px;
    }
  }
  
  &--large {
    .logo-icon {
      width: 60px;
      height: 60px;
      
      i {
        font-size: 28px;
      }
    }
    
    .logo-text {
      font-size: 24px;
      margin-left: 16px;
    }
  }
  
  // 样式变体
  &--default {
    .logo-icon {
      background: #1a1a1a;
      
      i {
        color: #fff;
      }
    }
    
    .logo-text {
      color: #1a1a1a;
    }
  }
  
  &--white {
    .logo-icon {
      background: #fff;
      border: 1px solid rgba(255, 255, 255, 0.2);
      
      i {
        color: #ff7a00;
      }
    }
    
    .logo-text {
      color: #fff;
    }
  }
  
  &--colored {
    .logo-icon {
      background: linear-gradient(135deg, #ff7a00 0%, #ff9500 100%);
      box-shadow: 0 4px 12px rgba(255, 122, 0, 0.3);
      
      i {
        color: #fff;
      }
    }
    
    .logo-text {
      background: linear-gradient(135deg, #ff7a00 0%, #ff9500 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
  
  &--minimal {
    .logo-icon {
      background: transparent;
      
      i {
        color: #ff7a00;
      }
    }
    
    .logo-text {
      color: #666;
      font-weight: 500;
    }
  }
  
  // 悬停效果
  &--clickable {
    &:hover {
      &.app-logo--default .logo-icon {
        background: #333;
      }
      
      &.app-logo--colored .logo-icon {
        box-shadow: 0 6px 20px rgba(255, 122, 0, 0.4);
      }
      
      &.app-logo--minimal .logo-icon i {
        color: rgba(255, 122, 0, 0.8);
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .app-logo {
    &--large {
      .logo-icon {
        width: 48px;
        height: 48px;
        
        i {
          font-size: 24px;
        }
      }
      
      .logo-text {
        font-size: 20px;
      }
    }
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .app-logo {
    &--default {
      .logo-text {
        color: #fff;
      }
    }
    
    &--minimal {
      .logo-text {
        color: #999;
      }
    }
  }
}
</style>
