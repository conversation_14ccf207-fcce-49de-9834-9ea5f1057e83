<template>
  <div 
    :class="[
      'user-avatar',
      `user-avatar--${size}`,
      `user-avatar--${shape}`,
      { 
        'user-avatar--clickable': clickable,
        'user-avatar--online': showOnlineStatus && isOnline,
        'user-avatar--bordered': bordered
      }
    ]"
    @click="handleClick"
  >
    <!-- 头像容器 -->
    <div class="avatar-container">
      <!-- 用户头像 -->
      <a-avatar
        :size="avatarSize"
        :src="src"
        :alt="alt"
        :style="{ backgroundColor: backgroundColor }"
        class="avatar-image"
      >
        {{ fallbackText }}
      </a-avatar>
      
      <!-- 在线状态指示器 -->
      <div 
        v-if="showOnlineStatus" 
        :class="[
          'online-indicator',
          { 'online-indicator--online': isOnline }
        ]"
      />
      
      <!-- 认证徽章 -->
      <div v-if="verified" class="verified-badge">
        <i class="fa fa-check"></i>
      </div>
    </div>
    
    <!-- 用户信息（可选） -->
    <div v-if="showInfo" class="user-info">
      <div class="user-name">{{ userName }}</div>
      <div v-if="userRole" class="user-role">{{ userRole }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

/**
 * 用户头像组件
 * 
 * @description 可复用的用户头像组件，支持多种尺寸、形状和状态
 * <AUTHOR> Way Team
 */

interface Props {
  /** 头像图片URL */
  src?: string
  /** 图片alt文本 */
  alt?: string
  /** 用户名（用于生成fallback文字和显示） */
  userName?: string
  /** 用户角色 */
  userRole?: string
  /** 头像尺寸 */
  size?: 'small' | 'medium' | 'large' | 'extra-large'
  /** 头像形状 */
  shape?: 'circle' | 'square'
  /** 是否可点击 */
  clickable?: boolean
  /** 是否显示在线状态 */
  showOnlineStatus?: boolean
  /** 是否在线 */
  isOnline?: boolean
  /** 是否已认证 */
  verified?: boolean
  /** 是否显示用户信息 */
  showInfo?: boolean
  /** 是否显示边框 */
  bordered?: boolean
  /** 自定义背景色 */
  backgroundColor?: string
}

interface Emits {
  /** 头像点击事件 */
  (e: 'click', userName?: string): void
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  shape: 'circle',
  clickable: false,
  showOnlineStatus: false,
  isOnline: false,
  verified: false,
  showInfo: false,
  bordered: false,
  backgroundColor: '#ff7a00'
})

const emit = defineEmits<Emits>()

// 计算Ant Design Avatar的尺寸
const avatarSize = computed(() => {
  const sizeMap = {
    small: 32,
    medium: 40,
    large: 64,
    'extra-large': 80
  }
  return sizeMap[props.size]
})

// 生成fallback文字
const fallbackText = computed(() => {
  if (!props.userName) return 'U'
  
  // 如果是中文名，取最后一个字符
  if (/[\u4e00-\u9fa5]/.test(props.userName)) {
    return props.userName.charAt(props.userName.length - 1)
  }
  
  // 如果是英文名，取首字母大写
  return props.userName.charAt(0).toUpperCase()
})

// 处理点击事件
const handleClick = () => {
  if (props.clickable) {
    emit('click', props.userName)
  }
}
</script>

<style lang="less" scoped>
.user-avatar {
  display: inline-flex;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &--clickable {
    cursor: pointer;
    
    &:hover {
      transform: translateY(-1px);
      
      .avatar-container {
        filter: brightness(1.1);
      }
    }
    
    &:active {
      transform: translateY(0);
    }
  }
  
  .avatar-container {
    position: relative;
    display: inline-block;
    
    .avatar-image {
      transition: all 0.3s ease;
      
      :deep(.ant-avatar-string) {
        font-weight: 600;
      }
    }
    
    // 在线状态指示器
    .online-indicator {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #d9d9d9;
      border: 2px solid #fff;
      transition: all 0.3s ease;
      
      &--online {
        background: #52c41a;
        box-shadow: 0 0 0 1px rgba(82, 196, 26, 0.3);
      }
    }
    
    // 认证徽章
    .verified-badge {
      position: absolute;
      top: -2px;
      right: -2px;
      width: 16px;
      height: 16px;
      background: #1890ff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid #fff;
      
      i {
        font-size: 8px;
        color: #fff;
      }
    }
  }
  
  .user-info {
    margin-left: 12px;
    
    .user-name {
      font-size: 14px;
      font-weight: 500;
      color: #1a1a1a;
      line-height: 1.4;
      margin-bottom: 2px;
    }
    
    .user-role {
      font-size: 12px;
      color: #666;
      line-height: 1.2;
    }
  }
  
  // 尺寸变体
  &--small {
    .avatar-container {
      .online-indicator {
        width: 8px;
        height: 8px;
        border-width: 1px;
      }
      
      .verified-badge {
        width: 12px;
        height: 12px;
        top: -1px;
        right: -1px;
        
        i {
          font-size: 6px;
        }
      }
    }
    
    .user-info {
      margin-left: 8px;
      
      .user-name {
        font-size: 12px;
      }
      
      .user-role {
        font-size: 11px;
      }
    }
  }
  
  &--medium {
    .avatar-container {
      .online-indicator {
        width: 12px;
        height: 12px;
      }
      
      .verified-badge {
        width: 16px;
        height: 16px;
        
        i {
          font-size: 8px;
        }
      }
    }
  }
  
  &--large {
    .avatar-container {
      .online-indicator {
        width: 16px;
        height: 16px;
        border-width: 3px;
      }
      
      .verified-badge {
        width: 20px;
        height: 20px;
        top: -3px;
        right: -3px;
        
        i {
          font-size: 10px;
        }
      }
    }
    
    .user-info {
      margin-left: 16px;
      
      .user-name {
        font-size: 16px;
      }
      
      .user-role {
        font-size: 13px;
      }
    }
  }
  
  &--extra-large {
    .avatar-container {
      .online-indicator {
        width: 20px;
        height: 20px;
        border-width: 3px;
      }
      
      .verified-badge {
        width: 24px;
        height: 24px;
        top: -4px;
        right: -4px;
        
        i {
          font-size: 12px;
        }
      }
    }
    
    .user-info {
      margin-left: 20px;
      
      .user-name {
        font-size: 18px;
      }
      
      .user-role {
        font-size: 14px;
      }
    }
  }
  
  // 形状变体
  &--square {
    .avatar-container .avatar-image {
      :deep(.ant-avatar) {
        border-radius: 8px;
      }
    }
  }
  
  // 边框样式
  &--bordered {
    .avatar-container .avatar-image {
      :deep(.ant-avatar) {
        border: 2px solid #f0f0f0;
      }
    }
    
    &.user-avatar--clickable:hover .avatar-container .avatar-image {
      :deep(.ant-avatar) {
        border-color: #ff7a00;
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .user-avatar {
    &--large {
      .user-info {
        .user-name {
          font-size: 15px;
        }
      }
    }
    
    &--extra-large {
      .user-info {
        .user-name {
          font-size: 16px;
        }
      }
    }
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .user-avatar {
    .user-info {
      .user-name {
        color: #fff;
      }
      
      .user-role {
        color: #999;
      }
    }
    
    &--bordered .avatar-container .avatar-image {
      :deep(.ant-avatar) {
        border-color: #333;
      }
    }
  }
}
</style>
