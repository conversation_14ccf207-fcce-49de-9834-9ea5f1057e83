<template>
  <div 
    :class="[
      'search-input',
      `search-input--${size}`,
      { 
        'search-input--focused': focused,
        'search-input--loading': loading
      }
    ]"
  >
    <!-- 搜索输入框 -->
    <a-input
      ref="inputRef"
      v-model:value="inputValue"
      :placeholder="placeholder"
      :size="size"
      :disabled="disabled"
      :allow-clear="allowClear"
      class="search-input-field"
      @focus="handleFocus"
      @blur="handleBlur"
      @input="handleInput"
      @pressEnter="handleSearch"
      @clear="handleClear"
    >
      <!-- 前缀搜索图标 -->
      <template #prefix>
        <SearchOutlined 
          :class="[
            'search-icon',
            { 'search-icon--loading': loading }
          ]" 
        />
      </template>
      
      <!-- 后缀操作区域 -->
      <template #suffix>
        <div class="search-suffix">
          <!-- 加载状态 -->
          <a-spin 
            v-if="loading" 
            :size="size === 'large' ? 'default' : 'small'"
            class="search-loading"
          />
          
          <!-- 搜索建议数量 -->
          <span 
            v-else-if="showResultCount && resultCount !== null" 
            class="result-count"
          >
            {{ resultCount }}
          </span>
          
          <!-- 搜索按钮 -->
          <a-button
            v-if="showSearchButton"
            type="primary"
            :size="size"
            :loading="loading"
            :disabled="!inputValue.trim()"
            @click="handleSearch"
            class="search-button"
          >
            {{ searchButtonText }}
          </a-button>
        </div>
      </template>
    </a-input>
    
    <!-- 搜索建议下拉列表 -->
    <div 
      v-if="showSuggestions && suggestions.length > 0"
      class="search-suggestions"
    >
      <div 
        v-for="(suggestion, index) in suggestions"
        :key="index"
        :class="[
          'suggestion-item',
          { 'suggestion-item--highlighted': index === highlightedIndex }
        ]"
        @click="selectSuggestion(suggestion)"
        @mouseenter="highlightedIndex = index"
      >
        <!-- 建议项图标 -->
        <div class="suggestion-icon">
          <i :class="suggestion.icon || 'fa fa-search'"></i>
        </div>
        
        <!-- 建议项内容 -->
        <div class="suggestion-content">
          <div class="suggestion-text" v-html="highlightText(suggestion.text)"></div>
          <div v-if="suggestion.description" class="suggestion-description">
            {{ suggestion.description }}
          </div>
        </div>
        
        <!-- 建议项类型标签 -->
        <div v-if="suggestion.type" class="suggestion-type">
          {{ suggestion.type }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import { debounce } from 'lodash-es'

/**
 * 搜索输入框组件
 * 
 * @description 功能丰富的搜索输入框，支持实时搜索、建议列表、加载状态等
 * <AUTHOR> Way Team
 */

interface SearchSuggestion {
  /** 建议文本 */
  text: string
  /** 建议描述 */
  description?: string
  /** 建议类型 */
  type?: string
  /** 建议图标 */
  icon?: string
  /** 建议值（用于选中时返回） */
  value?: any
}

interface Props {
  /** 输入框值 */
  modelValue?: string
  /** 占位符文本 */
  placeholder?: string
  /** 输入框尺寸 */
  size?: 'small' | 'middle' | 'large'
  /** 是否禁用 */
  disabled?: boolean
  /** 是否显示清除按钮 */
  allowClear?: boolean
  /** 是否显示搜索按钮 */
  showSearchButton?: boolean
  /** 搜索按钮文本 */
  searchButtonText?: string
  /** 是否加载中 */
  loading?: boolean
  /** 搜索建议列表 */
  suggestions?: SearchSuggestion[]
  /** 是否显示建议列表 */
  showSuggestions?: boolean
  /** 是否显示结果数量 */
  showResultCount?: boolean
  /** 结果数量 */
  resultCount?: number | null
  /** 搜索防抖延迟（毫秒） */
  debounceDelay?: number
}

interface Emits {
  /** 输入值变化 */
  (e: 'update:modelValue', value: string): void
  /** 搜索事件 */
  (e: 'search', value: string): void
  /** 输入事件（用于实时搜索） */
  (e: 'input', value: string): void
  /** 清除事件 */
  (e: 'clear'): void
  /** 选择建议项 */
  (e: 'select-suggestion', suggestion: SearchSuggestion): void
  /** 获得焦点 */
  (e: 'focus'): void
  /** 失去焦点 */
  (e: 'blur'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '搜索...',
  size: 'middle',
  disabled: false,
  allowClear: true,
  showSearchButton: false,
  searchButtonText: '搜索',
  loading: false,
  suggestions: () => [],
  showSuggestions: false,
  showResultCount: false,
  resultCount: null,
  debounceDelay: 300
})

const emit = defineEmits<Emits>()

// 组件引用
const inputRef = ref()

// 响应式状态
const focused = ref(false)
const highlightedIndex = ref(-1)

// 双向绑定的输入值
const inputValue = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value)
})

// 防抖搜索函数
const debouncedSearch = debounce((value: string) => {
  emit('input', value)
}, props.debounceDelay)

// 监听输入值变化，触发实时搜索
watch(inputValue, (newValue) => {
  if (newValue.trim()) {
    debouncedSearch(newValue.trim())
  }
}, { immediate: false })

// 事件处理器
const handleFocus = () => {
  focused.value = true
  emit('focus')
}

const handleBlur = () => {
  // 延迟失焦，防止点击建议项时失焦
  setTimeout(() => {
    focused.value = false
    highlightedIndex.value = -1
    emit('blur')
  }, 150)
}

const handleInput = (e: Event) => {
  const value = (e.target as HTMLInputElement).value
  inputValue.value = value
}

const handleSearch = () => {
  const value = inputValue.value.trim()
  if (value) {
    emit('search', value)
  }
}

const handleClear = () => {
  highlightedIndex.value = -1
  emit('clear')
}

const selectSuggestion = (suggestion: SearchSuggestion) => {
  inputValue.value = suggestion.text
  highlightedIndex.value = -1
  emit('select-suggestion', suggestion)
  emit('search', suggestion.text)
  
  // 聚焦输入框
  nextTick(() => {
    inputRef.value?.focus()
  })
}

// 高亮匹配文本
const highlightText = (text: string): string => {
  if (!inputValue.value.trim()) return text
  
  const searchValue = inputValue.value.trim()
  const regex = new RegExp(`(${searchValue})`, 'gi')
  return text.replace(regex, '<mark class="highlight">$1</mark>')
}

// 键盘导航
const handleKeydown = (e: KeyboardEvent) => {
  if (!props.showSuggestions || props.suggestions.length === 0) return
  
  switch (e.key) {
    case 'ArrowDown':
      e.preventDefault()
      highlightedIndex.value = Math.min(
        highlightedIndex.value + 1,
        props.suggestions.length - 1
      )
      break
    case 'ArrowUp':
      e.preventDefault()
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1)
      break
    case 'Enter':
      e.preventDefault()
      if (highlightedIndex.value >= 0) {
        selectSuggestion(props.suggestions[highlightedIndex.value])
      } else {
        handleSearch()
      }
      break
    case 'Escape':
      highlightedIndex.value = -1
      inputRef.value?.blur()
      break
  }
}

// 暴露方法
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur(),
  clear: () => {
    inputValue.value = ''
    handleClear()
  }
})
</script>

<style lang="less" scoped>
.search-input {
  position: relative;
  width: 100%;
  
  .search-input-field {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    :deep(.ant-input) {
      padding-left: 40px;
      
      &:focus {
        border-color: #ff7a00;
        box-shadow: 0 0 0 2px rgba(255, 122, 0, 0.2);
      }
    }
    
    .search-icon {
      color: #999;
      transition: all 0.3s ease;
      
      &--loading {
        animation: pulse 1.5s infinite;
      }
    }
    
    .search-suffix {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .result-count {
        font-size: 12px;
        color: #666;
        padding: 2px 6px;
        background: #f5f5f5;
        border-radius: 10px;
        min-width: 20px;
        text-align: center;
      }
      
      .search-button {
        background: #ff7a00;
        border-color: #ff7a00;
        
        &:hover {
          background: rgba(255, 122, 0, 0.9);
          border-color: rgba(255, 122, 0, 0.9);
        }
      }
    }
  }
  
  // 搜索建议下拉列表
  .search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    margin-top: 4px;
    
    .suggestion-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:hover,
      &--highlighted {
        background: #fff2e8;
      }
      
      .suggestion-icon {
        width: 20px;
        margin-right: 12px;
        text-align: center;
        
        i {
          color: #999;
          font-size: 14px;
        }
      }
      
      .suggestion-content {
        flex: 1;
        
        .suggestion-text {
          font-size: 14px;
          color: #1a1a1a;
          line-height: 1.4;
          
          :deep(.highlight) {
            background: #ff7a00;
            color: #fff;
            padding: 1px 2px;
            border-radius: 2px;
            font-weight: 500;
          }
        }
        
        .suggestion-description {
          font-size: 12px;
          color: #666;
          margin-top: 2px;
          line-height: 1.3;
        }
      }
      
      .suggestion-type {
        font-size: 11px;
        color: #999;
        background: #f5f5f5;
        padding: 2px 6px;
        border-radius: 4px;
        margin-left: 8px;
      }
    }
  }
  
  // 尺寸变体
  &--small {
    .search-input-field :deep(.ant-input) {
      padding-left: 32px;
    }
    
    .search-suggestions .suggestion-item {
      padding: 8px 12px;
      
      .suggestion-icon {
        width: 16px;
        margin-right: 8px;
        
        i {
          font-size: 12px;
        }
      }
      
      .suggestion-content .suggestion-text {
        font-size: 13px;
      }
    }
  }
  
  &--large {
    .search-input-field :deep(.ant-input) {
      padding-left: 48px;
    }
    
    .search-suggestions .suggestion-item {
      padding: 16px 20px;
      
      .suggestion-icon {
        width: 24px;
        margin-right: 16px;
        
        i {
          font-size: 16px;
        }
      }
      
      .suggestion-content .suggestion-text {
        font-size: 15px;
      }
    }
  }
  
  // 聚焦状态
  &--focused {
    .search-input-field {
      .search-icon {
        color: #ff7a00;
      }
    }
  }
}

// 动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .search-input {
    .search-suggestions {
      max-height: 200px;
      
      .suggestion-item {
        padding: 10px 12px;
      }
    }
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .search-input {
    .search-suggestions {
      background: #1f1f1f;
      border-color: #333;
      
      .suggestion-item {
        border-bottom-color: #333;
        
        &:hover,
        &--highlighted {
          background: #2a2a2a;
        }
        
        .suggestion-content {
          .suggestion-text {
            color: #fff;
          }
          
          .suggestion-description {
            color: #999;
          }
        }
        
        .suggestion-type {
          background: #333;
          color: #999;
        }
      }
    }
  }
}
</style>
