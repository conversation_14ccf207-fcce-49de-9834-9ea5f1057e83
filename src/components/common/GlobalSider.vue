<template>
    <div id="global-sider">
        <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      width="256"
      class="app-sider"
    >
      <div class="logo">
        <AppLogo 
          :size="collapsed ? 'small' : 'medium'"
          variant="colored"
          :showText="!collapsed"
          text="Recipe Hub"
        />
      </div>
      
      <a-menu
        v-model:selectedKeys="selectedKeys"
        :mode="'inline'"
        theme="light"
        class="app-menu"
      >
        <a-menu-item key="dashboard" @click="$router.push('/app/dashboard')">
          <i class="fa fa-home"></i>
          <span>仪表板</span>
        </a-menu-item>
        
        <a-menu-item key="recipes" @click="$router.push('/app/recipes')">
          <i class="fa fa-book"></i>
          <span>食谱浏览</span>
        </a-menu-item>
        
        <a-menu-item key="collections" @click="$router.push('/app/recipegallery')">
          <i class="fa fa-bookmark"></i>
          <span>我的食谱</span>
        </a-menu-item>
        
        <a-menu-item key="favorites" @click="$router.push('/app/favorites')">
          <i class="fa fa-star"></i>
          <span>收藏夹</span>
        </a-menu-item>
        
        <a-menu-item key="grocery" @click="$router.push('/app/grocery')">
          <i class="fa fa-shopping-cart"></i>
          <span>购物清单</span>
        </a-menu-item>
        
        <a-menu-divider />
        
        <a-menu-item key="profile" @click="$router.push('/app/profile')">
          <i class="fa fa-user"></i>
          <span>个人资料</span>
        </a-menu-item>
        
        <a-menu-item key="logout" @click="handleLogout">
          <i class="fa fa-sign-out"></i>
          <span>退出登录</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>
    </div>
    </template>

<script setup lang="ts">
import { ref, computed, watch, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

import { AppLogo, UserAvatar } from '@/components'

/**
 * 应用主布局组件
 * 包含侧边栏导航、顶部工具栏和主内容区域
 */

const route = useRoute()
const router = useRouter()

// 响应式状态
const collapsed = ref(false)
const selectedKeys = ref<string[]>(['dashboard'])

// 监听路由变化，更新选中的菜单项
// watch(
//   () => route.path,
//   (newPath) => {
//     if (newPath.includes('/app/dashboard')) {
//       selectedKeys.value = ['dashboard']
//     } else if (newPath.includes('/app/recipes')) {
//       selectedKeys.value = ['recipes']
//     } else if (newPath.includes('/app/collections')) {
//       selectedKeys.value = ['collections']
//     } else if (newPath.includes('/app/favorites')) {
//       selectedKeys.value = ['favorites']
//     } else if (newPath.includes('/app/grocery')) {
//       selectedKeys.value = ['grocery']
//     } else if (newPath.includes('/app/profile')) {
//       selectedKeys.value = ['profile']
//     }
//   },
//   { immediate: true }
// )

// 事件处理
const handleLogout = async () => {
  try {
    // TODO: 调用登出API
    // await userStore.logout()
    message.success('退出登录成功')
    await router.push('/user/login')
  } catch (error) {
    message.error('退出登录失败')
  }
}
</script>

<style lang="less" scoped>


.app-sider {
  background: #fff;
  box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);
  
  .logo {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .app-menu {
    border-right: none;
    padding: 16px 0;
    
    :deep(.ant-menu-item) {
      margin: 4px 12px;
      border-radius: 8px;
      
      i {
        margin-right: 8px;
        width: 16px;
        text-align: center;
      }
      
      &:hover,
      &.ant-menu-item-selected {
        background-color: #fff2e8;
        color: #ff7a00;
      }
    }
  }
}







// 响应式适配
@media (max-width: 768px) {
  .app-sider {
    position: fixed;
    z-index: 1000;
    height: 100vh;
  }
  
 
}
</style>
