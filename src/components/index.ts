/**
 * 组件统一导出文件
 * 
 * @description 统一管理和导出所有可复用组件
 * <AUTHOR> Way Team
 */

// 通用组件
export { default as AppLogo } from './common/AppLogo.vue'
export { default as UserAvatar } from './common/UserAvatar.vue'
export { default as SearchInput } from './common/SearchInput.vue'
export { default as RecipeCard } from './common/RecipeCard.vue'
export { default as GlobalHeader } from './common/GlobalHeader.vue'
export { default as GlobalSider } from './common/GlobalSider.vue'

// 组件类型定义
export type { default as AppLogoProps } from './common/AppLogo.vue'
export type { default as UserAvatarProps } from './common/UserAvatar.vue'
export type { default as SearchInputProps } from './common/SearchInput.vue'
export type { default as RecipeCardProps } from './common/RecipeCard.vue'
export type { default as GlobalHeaderProps } from './common/GlobalHeader.vue'
export type { default as GlobalSiderProps } from './common/GlobalSider.vue'
/**
 * 使用示例:
 * 
 * import { AppLogo, UserAvatar, SearchInput, RecipeCard } from '@/components'
 * 
 * 或者按需引入:
 * import AppLogo from '@/components/common/AppLogo.vue'
 */
