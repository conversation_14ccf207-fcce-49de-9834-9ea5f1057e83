// @ts-ignore
/* eslint-disable */
import request from '@/request'

/** getRecipeSteps GET /api/recipe/${param0}/steps */
export async function getRecipeStepsUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getRecipeStepsUsingGETParams,
  options?: { [key: string]: any }
) {
  const { recipeId: param0, ...queryParams } = params
  return request<API.BaseResponseListRecipeStepVO_>(`/api/recipe/${param0}/steps`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  })
}

/** saveRecipeSteps POST /api/recipe/${param0}/steps/save */
export async function saveRecipeStepsUsingPost(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.saveRecipeStepsUsingPOSTParams,
  body: API.RecipeStepBulkSaveRequest,
  options?: { [key: string]: any }
) {
  const { recipeId: param0, ...queryParams } = params
  return request<API.BaseResponseListRecipeStepVO_>(`/api/recipe/${param0}/steps/save`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  })
}

/** addRecipe POST /api/recipe/add */
export async function addRecipeUsingPost(
  body: API.RecipeAddRequest,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponseRecipeVO_>('/api/recipe/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** deleteRecipe POST /api/recipe/delete */
export async function deleteRecipeUsingPost(
  body: API.DeleteRequest,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponseBoolean_>('/api/recipe/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** deleteRecipeByAdmin POST /api/recipe/delete/admin */
export async function deleteRecipeByAdminUsingPost(
  body: API.DeleteRequest,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponseBoolean_>('/api/recipe/delete/admin', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** getRecipe GET /api/recipe/get/${param0} */
export async function getRecipeUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getRecipeUsingGETParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params
  return request<API.BaseResponseRecipeDetailVO_>(`/api/recipe/get/${param0}`, {
    method: 'GET',
    params: { ...queryParams },
    ...(options || {}),
  })
}

/** listRecipes POST /api/recipe/list/page */
export async function listRecipesUsingPost(
  body: API.RecipeQueryRequest,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponsePageRecipeVO_>('/api/recipe/list/page', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** listRecipesByAdmin POST /api/recipe/list/page/admin */
export async function listRecipesByAdminUsingPost(
  body: API.RecipeQueryRequest,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponsePageRecipeVO_>('/api/recipe/list/page/admin', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}

/** updateRecipe POST /api/recipe/update */
export async function updateRecipeUsingPost(
  body: API.RecipeUpdateRequest,
  options?: { [key: string]: any }
) {
  return request<API.BaseResponseBoolean_>('/api/recipe/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  })
}
