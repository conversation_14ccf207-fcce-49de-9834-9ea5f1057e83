declare namespace API {
  type BaseResponseBoolean_ = {
    code?: number
    data?: boolean
    message?: string
  }

  type BaseResponseInt_ = {
    code?: number
    data?: number
    message?: string
  }

  type BaseResponseListRecipeStepVO_ = {
    code?: number
    data?: RecipeStepVO[]
    message?: string
  }

  type BaseResponseLoginUserVO_ = {
    code?: number
    data?: LoginUserVO
    message?: string
  }

  type BaseResponseLong_ = {
    code?: number
    data?: number
    message?: string
  }

  type BaseResponsePagePost_ = {
    code?: number
    data?: PagePost_
    message?: string
  }

  type BaseResponsePagePostVO_ = {
    code?: number
    data?: PagePostVO_
    message?: string
  }

  type BaseResponsePageRecipeVO_ = {
    code?: number
    data?: PageRecipeVO_
    message?: string
  }

  type BaseResponsePageUser_ = {
    code?: number
    data?: PageUser_
    message?: string
  }

  type BaseResponsePageUserVO_ = {
    code?: number
    data?: PageUserVO_
    message?: string
  }

  type BaseResponsePostVO_ = {
    code?: number
    data?: PostVO
    message?: string
  }

  type BaseResponseRecipeDetailVO_ = {
    code?: number
    data?: RecipeDetailVO
    message?: string
  }

  type BaseResponseRecipeVO_ = {
    code?: number
    data?: RecipeVO
    message?: string
  }

  type BaseResponseString_ = {
    code?: number
    data?: string
    message?: string
  }

  type BaseResponseUser_ = {
    code?: number
    data?: User
    message?: string
  }

  type BaseResponseUserVO_ = {
    code?: number
    data?: UserVO
    message?: string
  }

  type checkUsingGETParams = {
    /** echostr */
    echostr?: string
    /** nonce */
    nonce?: string
    /** signature */
    signature?: string
    /** timestamp */
    timestamp?: string
  }

  type DeleteRequest = {
    id?: number
  }

  type getPostVOByIdUsingGETParams = {
    /** id */
    id?: number
  }

  type getRecipeStepsUsingGETParams = {
    /** recipeId */
    recipeId: number
  }

  type getRecipeUsingGETParams = {
    /** id */
    id: number
  }

  type getUserByIdUsingGETParams = {
    /** id */
    id?: number
  }

  type getUserVOByIdUsingGETParams = {
    /** id */
    id?: number
  }

  type LoginUserVO = {
    createTime?: string
    id?: number
    updateTime?: string
    userAvatar?: string
    userName?: string
    userProfile?: string
    userRole?: string
  }

  type OrderItem = {
    asc?: boolean
    column?: string
  }

  type PagePost_ = {
    countId?: string
    current?: number
    maxLimit?: number
    optimizeCountSql?: boolean
    orders?: OrderItem[]
    pages?: number
    records?: Post[]
    searchCount?: boolean
    size?: number
    total?: number
  }

  type PagePostVO_ = {
    countId?: string
    current?: number
    maxLimit?: number
    optimizeCountSql?: boolean
    orders?: OrderItem[]
    pages?: number
    records?: PostVO[]
    searchCount?: boolean
    size?: number
    total?: number
  }

  type PageRecipeVO_ = {
    countId?: string
    current?: number
    maxLimit?: number
    optimizeCountSql?: boolean
    orders?: OrderItem[]
    pages?: number
    records?: RecipeVO[]
    searchCount?: boolean
    size?: number
    total?: number
  }

  type PageUser_ = {
    countId?: string
    current?: number
    maxLimit?: number
    optimizeCountSql?: boolean
    orders?: OrderItem[]
    pages?: number
    records?: User[]
    searchCount?: boolean
    size?: number
    total?: number
  }

  type PageUserVO_ = {
    countId?: string
    current?: number
    maxLimit?: number
    optimizeCountSql?: boolean
    orders?: OrderItem[]
    pages?: number
    records?: UserVO[]
    searchCount?: boolean
    size?: number
    total?: number
  }

  type Post = {
    content?: string
    createTime?: string
    favourNum?: number
    id?: number
    isDelete?: number
    tags?: string
    thumbNum?: number
    title?: string
    updateTime?: string
    userId?: number
  }

  type PostAddRequest = {
    content?: string
    tags?: string[]
    title?: string
  }

  type PostEditRequest = {
    content?: string
    id?: number
    tags?: string[]
    title?: string
  }

  type PostFavourAddRequest = {
    postId?: number
  }

  type PostFavourQueryRequest = {
    current?: number
    pageSize?: number
    postQueryRequest?: PostQueryRequest
    sortField?: string
    sortOrder?: string
    userId?: number
  }

  type PostQueryRequest = {
    content?: string
    current?: number
    favourUserId?: number
    id?: number
    notId?: number
    orTags?: string[]
    pageSize?: number
    searchText?: string
    sortField?: string
    sortOrder?: string
    tags?: string[]
    title?: string
    userId?: number
  }

  type PostThumbAddRequest = {
    postId?: number
  }

  type PostUpdateRequest = {
    content?: string
    id?: number
    tags?: string[]
    title?: string
  }

  type PostVO = {
    content?: string
    createTime?: string
    favourNum?: number
    hasFavour?: boolean
    hasThumb?: boolean
    id?: number
    tagList?: string[]
    thumbNum?: number
    title?: string
    updateTime?: string
    user?: UserVO
    userId?: number
  }

  type RecipeAddRequest = {
    category?: string
    coverKey?: string
    introduction?: string
    name?: string
    tags?: string[]
  }

  type RecipeDetailVO = {
    category?: string
    coverKey?: string
    createTime?: string
    id?: number
    introduction?: string
    name?: string
    stepCount?: number
    steps?: RecipeStepVO[]
    tags?: string[]
    updateTime?: string
  }

  type RecipeQueryRequest = {
    category?: string
    current?: number
    id?: number
    introduction?: string
    name?: string
    pageSize?: number
    searchText?: string
    sortField?: string
    sortOrder?: string
    tags?: string[]
    userId?: number
  }

  type RecipeStepBulkSaveRequest = {
    items?: RecipeStepUpsertItem[]
    recipeId?: number
  }

  type RecipeStepUpsertItem = {
    estMinutes?: number
    id?: number
    note?: string
    parallelizable?: boolean
    title?: string
  }

  type RecipeStepVO = {
    createTime?: string
    estMinutes?: number
    note?: string
    orderIndex?: number
    parallelizable?: number
    recipeId?: number
    title?: string
    updateTime?: string
  }

  type RecipeUpdateRequest = {
    category?: string
    coverKey?: string
    id?: number
    introduction?: string
    name?: string
    tags?: string[]
  }

  type RecipeVO = {
    category?: string
    coverKey?: string
    createTime?: string
    id?: number
    introduction?: string
    name?: string
    stepCount?: number
    tags?: string[]
    updateTime?: string
  }

  type saveRecipeStepsUsingPOSTParams = {
    /** recipeId */
    recipeId: number
  }

  type uploadFileUsingPOSTParams = {
    biz?: string
  }

  type User = {
    createTime?: string
    id?: number
    isDelete?: number
    mpOpenId?: string
    unionId?: string
    updateTime?: string
    userAccount?: string
    userAvatar?: string
    userName?: string
    userPassword?: string
    userProfile?: string
    userRole?: string
  }

  type UserAddRequest = {
    userAccount?: string
    userAvatar?: string
    userName?: string
    userRole?: string
  }

  type userLoginByWxOpenUsingGETParams = {
    /** code */
    code: string
  }

  type UserLoginRequest = {
    userAccount?: string
    userPassword?: string
  }

  type UserQueryRequest = {
    current?: number
    id?: number
    mpOpenId?: string
    pageSize?: number
    sortField?: string
    sortOrder?: string
    unionId?: string
    userName?: string
    userProfile?: string
    userRole?: string
  }

  type UserRegisterRequest = {
    checkPassword?: string
    userAccount?: string
    userPassword?: string
  }

  type UserUpdateMyRequest = {
    userAvatar?: string
    userName?: string
    userProfile?: string
  }

  type UserUpdateRequest = {
    id?: number
    userAvatar?: string
    userName?: string
    userProfile?: string
    userRole?: string
  }

  type UserVO = {
    createTime?: string
    id?: number
    userAvatar?: string
    userName?: string
    userProfile?: string
    userRole?: string
  }
}
